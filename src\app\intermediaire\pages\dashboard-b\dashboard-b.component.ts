import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IntermediaireService } from '../../services/intermediaire.service';
import { KpiDashboardDTO, HistoriqueExecutionDTO, NotificationRequestDTO } from '../../models/dashboard';
import { OrdreResponseDTO } from '../../../investisseur/models/ordre';

// PrimeNG Imports
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { DropdownModule } from 'primeng/dropdown';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ChartModule } from 'primeng/chart';
import { ProgressBarModule } from 'primeng/progressbar';
import { TagModule } from 'primeng/tag';
import { SkeletonModule } from 'primeng/skeleton';

@Component({
  selector: 'app-dashboard-b',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CardModule,
    ButtonModule,
    TableModule,
    DialogModule,
    InputTextModule,
    InputTextareaModule,
    DropdownModule,
    ToastModule,
    ConfirmDialogModule,
    ChartModule,
    ProgressBarModule,
    TagModule,
    SkeletonModule
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './dashboard-b.component.html',
  styleUrl: './dashboard-b.component.css'
})
export class DashboardBComponent implements OnInit {
  // ===== DONNÉES KPI =====
  kpiData?: KpiDashboardDTO;
  isLoadingKpi = true;

  // ===== HISTORIQUE D'EXÉCUTION =====
  historiqueExecution: HistoriqueExecutionDTO[] = [];
  isLoadingHistorique = true;

  // ===== ORDRES POUR ANNULATION =====
  ordresValides: OrdreResponseDTO[] = [];
  isLoadingOrdres = true;

  // ===== DIALOGS =====
  showAnnulationDialog = false;
  showNotificationDialog = false;
  selectedOrdreForAnnulation?: OrdreResponseDTO;

  // ===== NOTIFICATION =====
  notificationRequest: NotificationRequestDTO = {
    userId: '',
    message: ''
  };
  availableUsers: any[] = []; // À remplir avec les utilisateurs disponibles

  // ===== GRAPHIQUES =====
  chartData: any;
  chartOptions: any;

  // ===== COULEURS ULTRA-MODERNES =====
  readonly modernColors = {
    primary: '#1e3a8a',
    secondary: '#3b82f6',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    info: '#8b5cf6',
    light: '#f8fafc',
    dark: '#1e293b'
  };

  constructor(
    private intermediaireService: IntermediaireService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService
  ) { }

  ngOnInit(): void {
    this.loadKpiData();
    this.loadHistoriqueExecution();
    this.loadOrdresValides();
    this.initializeChart();
  }

  // ===== CHARGEMENT DES DONNÉES =====
  loadKpiData(): void {
    this.isLoadingKpi = true;
    this.intermediaireService.getKpiDashboard().subscribe({
      next: (data) => {
        this.kpiData = data;
        this.isLoadingKpi = false;
        this.updateChartData();
      },
      error: (error) => {
        console.error('Erreur lors du chargement des KPI:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Erreur',
          detail: 'Impossible de charger les données KPI'
        });
        this.isLoadingKpi = false;
      }
    });
  }

  loadHistoriqueExecution(): void {
    this.isLoadingHistorique = true;
    this.intermediaireService.getHistoriqueExecution().subscribe({
      next: (data) => {
        this.historiqueExecution = data;
        this.isLoadingHistorique = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement de l\'historique:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Erreur',
          detail: 'Impossible de charger l\'historique d\'exécution'
        });
        this.isLoadingHistorique = false;
      }
    });
  }

  loadOrdresValides(): void {
    this.isLoadingOrdres = true;
    this.intermediaireService.getOrdresValides().subscribe({
      next: (data) => {
        this.ordresValides = data;
        this.isLoadingOrdres = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des ordres:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Erreur',
          detail: 'Impossible de charger les ordres'
        });
        this.isLoadingOrdres = false;
      }
    });
  }

  // ===== GESTION DES GRAPHIQUES =====
  initializeChart(): void {
    this.chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            usePointStyle: true,
            padding: 20,
            font: {
              size: 12,
              family: 'Inter, sans-serif'
            }
          }
        }
      },
      elements: {
        arc: {
          borderWidth: 0
        }
      }
    };
  }

  updateChartData(): void {
    if (!this.kpiData) return;

    this.chartData = {
      labels: [
        'Ordres en Attente',
        'Opérations en Exécution',
        'Opérations Traitées Aujourd\'hui',
        'Ordres Fractionnés Aujourd\'hui'
      ],
      datasets: [{
        data: [
          this.kpiData.ordresEnAttente,
          this.kpiData.operationsEnExecution,
          this.kpiData.operationsTraiteesAujourdhui,
          this.kpiData.ordresFractionnesAujourdhui
        ],
        backgroundColor: [
          this.modernColors.primary,
          this.modernColors.warning,
          this.modernColors.success,
          this.modernColors.info
        ],
        borderWidth: 0
      }]
    };
  }

  // ===== GESTION DES ACTIONS =====
  openAnnulationDialog(ordre: OrdreResponseDTO): void {
    this.selectedOrdreForAnnulation = ordre;
    this.showAnnulationDialog = true;
  }

  confirmAnnulationOrdre(): void {
    if (!this.selectedOrdreForAnnulation) return;

    this.confirmationService.confirm({
      message: `Êtes-vous sûr de vouloir annuler l'ordre ${this.selectedOrdreForAnnulation.id} ?`,
      header: 'Confirmation d\'annulation',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Oui, annuler',
      rejectLabel: 'Non',
      accept: () => {
        this.annulerOrdre();
      }
    });
  }

  annulerOrdre(): void {
    if (!this.selectedOrdreForAnnulation) return;

    this.intermediaireService.annulerOrdre(this.selectedOrdreForAnnulation.id).subscribe({
      next: (response) => {
        this.messageService.add({
          severity: 'success',
          summary: 'Succès',
          detail: response
        });
        this.showAnnulationDialog = false;
        this.selectedOrdreForAnnulation = undefined;
        this.loadOrdresValides(); // Recharger la liste
        this.loadKpiData(); // Mettre à jour les KPI
      },
      error: (error) => {
        console.error('Erreur lors de l\'annulation:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Erreur',
          detail: 'Impossible d\'annuler l\'ordre'
        });
      }
    });
  }

  openNotificationDialog(): void {
    this.notificationRequest = { userId: '', message: '' };
    this.showNotificationDialog = true;
  }

  envoyerNotification(): void {
    if (!this.notificationRequest.userId || !this.notificationRequest.message) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Attention',
        detail: 'Veuillez remplir tous les champs'
      });
      return;
    }

    this.intermediaireService.envoyerNotification(this.notificationRequest).subscribe({
      next: (response) => {
        this.messageService.add({
          severity: 'success',
          summary: 'Succès',
          detail: response
        });
        this.showNotificationDialog = false;
        this.notificationRequest = { userId: '', message: '' };
      },
      error: (error) => {
        console.error('Erreur lors de l\'envoi:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Erreur',
          detail: 'Impossible d\'envoyer la notification'
        });
      }
    });
  }

  // ===== MÉTHODES UTILITAIRES =====
  refreshAllData(): void {
    this.loadKpiData();
    this.loadHistoriqueExecution();
    this.loadOrdresValides();
  }

  getStatutSeverity(statut: string): "success" | "secondary" | "info" | "warning" | "danger" | "contrast" | undefined {
    switch (statut.toUpperCase()) {
      case 'EXECUTE':
        return 'success';
      case 'EN_EXECUTION':
        return 'warning';
      case 'VALIDE':
        return 'info';
      case 'ANNULE':
        return 'danger';
      default:
        return 'secondary';
    }
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('fr-FR');
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'TND'
    }).format(amount);
  }
}
