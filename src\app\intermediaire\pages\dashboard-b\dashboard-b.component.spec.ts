import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { MessageService, ConfirmationService } from 'primeng/api';
import { DashboardBComponent } from './dashboard-b.component';
import { IntermediaireService } from '../../services/intermediaire.service';
import { of } from 'rxjs';

describe('DashboardBComponent', () => {
  let component: DashboardBComponent;
  let fixture: ComponentFixture<DashboardBComponent>;
  let intermediaireService: jasmine.SpyObj<IntermediaireService>;

  const mockKpiData = {
    ordresEnAttente: 5,
    operationsEnExecution: 3,
    operationsTraiteesAujourdhui: 12,
    ordresFractionnesAujourdhui: 2
  };

  const mockHistorique = [
    {
      operationId: '123e4567-e89b-12d3-a456-426614174000',
      date: '2025-01-15',
      nbOrdres: 3,
      montant: 15000,
      statut: 'EXECUTE'
    }
  ];

  const mockOrdres = [
    {
      id: '123e4567-e89b-12d3-a456-426614174001',
      type: 'ACHAT',
      statut: 'VALIDE',
      quantite: 100,
      prixMin: 50,
      prixMax: 55,
      toutOuRien: false,
      dateDeReception: '2025-01-15T10:00:00',
      dateDeValidite: '2025-01-20T23:59:59',
      investisseurUsername: 'investor1',
      actionIsin: 'FR0000120404',
      actionNom: 'ACCOR'
    }
  ];

  beforeEach(async () => {
    const intermediaireServiceSpy = jasmine.createSpyObj('IntermediaireService', [
      'getKpiDashboard',
      'getHistoriqueExecution',
      'getOrdresValides',
      'annulerOrdre',
      'envoyerNotification'
    ]);

    await TestBed.configureTestingModule({
      imports: [DashboardBComponent, HttpClientTestingModule],
      providers: [
        { provide: IntermediaireService, useValue: intermediaireServiceSpy },
        MessageService,
        ConfirmationService
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DashboardBComponent);
    component = fixture.componentInstance;
    intermediaireService = TestBed.inject(IntermediaireService) as jasmine.SpyObj<IntermediaireService>;

    // Setup default mock responses
    intermediaireService.getKpiDashboard.and.returnValue(of(mockKpiData));
    intermediaireService.getHistoriqueExecution.and.returnValue(of(mockHistorique));
    intermediaireService.getOrdresValides.and.returnValue(of(mockOrdres));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load KPI data on init', () => {
    component.ngOnInit();
    expect(intermediaireService.getKpiDashboard).toHaveBeenCalled();
    expect(component.kpiData).toEqual(mockKpiData);
    expect(component.isLoadingKpi).toBeFalse();
  });

  it('should load historique execution on init', () => {
    component.ngOnInit();
    expect(intermediaireService.getHistoriqueExecution).toHaveBeenCalled();
    expect(component.historiqueExecution).toEqual(mockHistorique);
    expect(component.isLoadingHistorique).toBeFalse();
  });

  it('should load ordres valides on init', () => {
    component.ngOnInit();
    expect(intermediaireService.getOrdresValides).toHaveBeenCalled();
    expect(component.ordresValides).toEqual(mockOrdres);
    expect(component.isLoadingOrdres).toBeFalse();
  });

  it('should return correct severity for statut', () => {
    expect(component.getStatutSeverity('EXECUTE')).toBe('success');
    expect(component.getStatutSeverity('EN_EXECUTION')).toBe('warning');
    expect(component.getStatutSeverity('VALIDE')).toBe('info');
    expect(component.getStatutSeverity('ANNULE')).toBe('danger');
    expect(component.getStatutSeverity('UNKNOWN')).toBe('secondary');
  });

  it('should format currency correctly', () => {
    const formatted = component.formatCurrency(1000);
    expect(formatted).toContain('TND');
    expect(formatted).toContain('1');
  });

  it('should format date correctly', () => {
    const formattedDate = component.formatDate('2025-01-15');
    expect(formattedDate).toBe('15/01/2025');
  });

  it('should open annulation dialog', () => {
    const ordre = mockOrdres[0];
    component.openAnnulationDialog(ordre);
    expect(component.showAnnulationDialog).toBeTrue();
    expect(component.selectedOrdreForAnnulation).toEqual(ordre);
  });

  it('should open notification dialog', () => {
    component.openNotificationDialog();
    expect(component.showNotificationDialog).toBeTrue();
    expect(component.notificationRequest).toEqual({ userId: '', message: '' });
  });

  it('should refresh all data', () => {
    component.refreshAllData();
    expect(intermediaireService.getKpiDashboard).toHaveBeenCalled();
    expect(intermediaireService.getHistoriqueExecution).toHaveBeenCalled();
    expect(intermediaireService.getOrdresValides).toHaveBeenCalled();
  });
});
