export interface KpiDashboardDTO {
  ordresEnAttente: number;
  operationsEnExecution: number;
  operationsTraiteesAujourdhui: number;
  ordresFractionnesAujourdhui: number;
}

export interface HistoriqueExecutionDTO {
  operationId: string;
  date: string; // LocalDate sera converti en string
  nbOrdres: number;
  montant: number;
  statut: string;
}

export interface NotificationRequestDTO {
  userId: string;
  message: string;
}

export interface NotificationResponseDTO {
  message: string;
}
