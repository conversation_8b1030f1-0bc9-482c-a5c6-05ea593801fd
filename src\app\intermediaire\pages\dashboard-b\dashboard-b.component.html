<!-- Dashboard Ultra-Moderne Intermédiaire -->
<div class="dashboard-container">
    <!-- Header Section -->
    <div class="dashboard-header">
        <div class="header-content">
            <div class="title-section">
                <h1 class="dashboard-title">
                    <i class="pi pi-chart-line"></i>
                    Dashboard Intermédiaire
                </h1>
                <p class="dashboard-subtitle">Tableau de bord avancé pour la gestion des opérations</p>
            </div>
            <div class="header-actions">
                <p-button icon="pi pi-refresh" label="Actualiser" class="refresh-btn" (onClick)="refreshAllData()"
                    [loading]="isLoadingKpi">
                </p-button>
                <p-button icon="pi pi-bell" label="Notification" class="notification-btn"
                    (onClick)="openNotificationDialog()">
                </p-button>
            </div>
        </div>
    </div>

    <!-- KPI Cards Section -->
    <div class="kpi-section">
        <div class="kpi-grid">
            <!-- Ordres en Attente -->
            <div class="kpi-card primary-card">
                <div class="card-header">
                    <div class="icon-container primary-icon">
                        <i class="pi pi-clock"></i>
                    </div>
                    <div class="trend-indicator">
                        <i class="pi pi-arrow-up"></i>
                    </div>
                </div>
                <div class="card-content">
                    <h3 class="kpi-label">Ordres en Attente</h3>
                    <div class="kpi-value-container">
                        <span class="kpi-value" *ngIf="!isLoadingKpi">{{ kpiData?.ordresEnAttente || 0 }}</span>
                        <p-skeleton *ngIf="isLoadingKpi" width="4rem" height="2rem"></p-skeleton>
                    </div>
                    <div class="kpi-subtitle">En cours de traitement</div>
                </div>
                <div class="card-footer">
                    <div class="progress-bar">
                        <div class="progress-fill primary-progress" style="width: 65%"></div>
                    </div>
                </div>
            </div>

            <!-- Opérations en Exécution -->
            <div class="kpi-card warning-card">
                <div class="card-header">
                    <div class="icon-container warning-icon">
                        <i class="pi pi-cog"></i>
                    </div>
                    <div class="trend-indicator">
                        <i class="pi pi-arrow-right"></i>
                    </div>
                </div>
                <div class="card-content">
                    <h3 class="kpi-label">Opérations en Exécution</h3>
                    <div class="kpi-value-container">
                        <span class="kpi-value" *ngIf="!isLoadingKpi">{{ kpiData?.operationsEnExecution || 0 }}</span>
                        <p-skeleton *ngIf="isLoadingKpi" width="4rem" height="2rem"></p-skeleton>
                    </div>
                    <div class="kpi-subtitle">Actuellement en cours</div>
                </div>
                <div class="card-footer">
                    <div class="progress-bar">
                        <div class="progress-fill warning-progress" style="width: 45%"></div>
                    </div>
                </div>
            </div>

            <!-- Opérations Traitées Aujourd'hui -->
            <div class="kpi-card success-card">
                <div class="card-header">
                    <div class="icon-container success-icon">
                        <i class="pi pi-check-circle"></i>
                    </div>
                    <div class="trend-indicator positive">
                        <i class="pi pi-arrow-up"></i>
                        <span>+12%</span>
                    </div>
                </div>
                <div class="card-content">
                    <h3 class="kpi-label">Opérations Traitées</h3>
                    <div class="kpi-value-container">
                        <span class="kpi-value" *ngIf="!isLoadingKpi">{{ kpiData?.operationsTraiteesAujourdhui || 0
                            }}</span>
                        <p-skeleton *ngIf="isLoadingKpi" width="4rem" height="2rem"></p-skeleton>
                    </div>
                    <div class="kpi-subtitle">Aujourd'hui</div>
                </div>
                <div class="card-footer">
                    <div class="progress-bar">
                        <div class="progress-fill success-progress" style="width: 85%"></div>
                    </div>
                </div>
            </div>

            <!-- Ordres Fractionnés -->
            <div class="kpi-card info-card">
                <div class="card-header">
                    <div class="icon-container info-icon">
                        <i class="pi pi-sitemap"></i>
                    </div>
                    <div class="trend-indicator">
                        <i class="pi pi-arrow-up"></i>
                    </div>
                </div>
                <div class="card-content">
                    <h3 class="kpi-label">Ordres Fractionnés</h3>
                    <div class="kpi-value-container">
                        <span class="kpi-value" *ngIf="!isLoadingKpi">{{ kpiData?.ordresFractionnesAujourdhui || 0
                            }}</span>
                        <p-skeleton *ngIf="isLoadingKpi" width="4rem" height="2rem"></p-skeleton>
                    </div>
                    <div class="kpi-subtitle">Aujourd'hui</div>
                </div>
                <div class="card-footer">
                    <div class="progress-bar">
                        <div class="progress-fill info-progress" style="width: 30%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="content-grid">

        <!-- Ordres Section -->
        <div class="ordres-section">
            <p-card class="modern-card">
                <ng-template pTemplate="header">
                    <div class="card-title-container">
                        <h3 class="card-title">
                            <i class="pi pi-list"></i>
                            Ordres Valides
                        </h3>
                        <p-button icon="pi pi-refresh" class="p-button-text p-button-sm" (onClick)="loadOrdresValides()"
                            [loading]="isLoadingOrdres">
                        </p-button>
                    </div>
                </ng-template>
                <p-table [value]="ordresValides" [loading]="isLoadingOrdres" [paginator]="true" [rows]="4"
                    [showCurrentPageReport]="true"
                    currentPageReportTemplate="Affichage de {first} à {last} sur {totalRecords} ordres"
                    styleClass="modern-table">

                    <ng-template pTemplate="header">
                        <tr>
                            <th>ID</th>
                            <th>Type</th>
                            <th>Investisseur</th>
                            <th>Action</th>
                            <th>Quantité</th>
                            <th>Prix Min</th>
                            <th>Prix Max</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </ng-template>

                    <ng-template pTemplate="body" let-ordre>
                        <tr>
                            <td>{{ ordre.id.substring(0, 8) }}...</td>
                            <td>
                                <p-tag [value]="ordre.type" [severity]="ordre.type === 'ACHAT' ? 'success' : 'info'">
                                </p-tag>
                            </td>
                            <td>{{ ordre.investisseurUsername }}</td>
                            <td>{{ ordre.actionNom }}</td>
                            <td>{{ ordre.quantite | number }}</td>
                            <td>{{ formatCurrency(ordre.prixMin) }}</td>
                            <td>{{ formatCurrency(ordre.prixMax) }}</td>
                            <td>
                                <p-tag [value]="ordre.statut" [severity]="getStatutSeverity(ordre.statut)">
                                </p-tag>
                            </td>
                            <td>
                                <p-button icon="pi pi-times" class="p-button-danger p-button-sm p-button-text"
                                    pTooltip="Annuler l'ordre" (onClick)="openAnnulationDialog(ordre)">
                                </p-button>
                            </td>
                        </tr>
                    </ng-template>

                    <ng-template pTemplate="emptymessage">
                        <tr>
                            <td colspan="9" class="text-center">Aucun ordre trouvé</td>
                        </tr>
                    </ng-template>
                </p-table>
            </p-card>
        </div>
    </div>

    <!-- Historique Section -->
    <div class="historique-section">
        <p-card class="modern-card">
            <ng-template pTemplate="header">
                <div class="card-title-container">
                    <h3 class="card-title">
                        <i class="pi pi-history"></i>
                        Historique d'Exécution
                    </h3>
                    <p-button icon="pi pi-refresh" class="p-button-text p-button-sm"
                        (onClick)="loadHistoriqueExecution()" [loading]="isLoadingHistorique">
                    </p-button>
                </div>
            </ng-template>
            <p-table [value]="historiqueExecution" [loading]="isLoadingHistorique" [paginator]="true" [rows]="10"
                [showCurrentPageReport]="true"
                currentPageReportTemplate="Affichage de {first} à {last} sur {totalRecords} opérations"
                styleClass="modern-table">

                <ng-template pTemplate="header">
                    <tr>
                        <th>ID Opération</th>
                        <th>Date</th>
                        <th>Nombre d'Ordres</th>
                        <th>Montant</th>
                        <th>Statut</th>
                    </tr>
                </ng-template>

                <ng-template pTemplate="body" let-historique>
                    <tr>
                        <td>{{ historique.operationId.substring(0, 8) }}...</td>
                        <td>{{ formatDate(historique.date) }}</td>
                        <td>{{ historique.nbOrdres }}</td>
                        <td>{{ formatCurrency(historique.montant) }}</td>
                        <td>
                            <p-tag [value]="historique.statut" [severity]="getStatutSeverity(historique.statut)">
                            </p-tag>
                        </td>
                    </tr>
                </ng-template>

                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="5" class="text-center">Aucun historique trouvé</td>
                    </tr>
                </ng-template>
            </p-table>
        </p-card>
    </div>
</div>

<!-- Dialog d'Annulation d'Ordre -->
<p-dialog header="Annuler un Ordre" [(visible)]="showAnnulationDialog" [modal]="true" [style]="{width: '450px'}"
    [draggable]="false" [resizable]="false" styleClass="modern-dialog">

    <div class="dialog-content" *ngIf="selectedOrdreForAnnulation">
        <div class="confirmation-message">
            <i class="pi pi-exclamation-triangle warning-icon"></i>
            <h4>Confirmation d'annulation</h4>
            <p>Êtes-vous sûr de vouloir annuler cet ordre ?</p>

            <div class="ordre-details">
                <div class="detail-row">
                    <span class="label">ID:</span>
                    <span class="value">{{ selectedOrdreForAnnulation.id.substring(0, 8) }}...</span>
                </div>
                <div class="detail-row">
                    <span class="label">Type:</span>
                    <span class="value">{{ selectedOrdreForAnnulation.type }}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Quantité:</span>
                    <span class="value">{{ selectedOrdreForAnnulation.quantite }}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Action:</span>
                    <span class="value">{{ selectedOrdreForAnnulation.actionNom }}</span>
                </div>
            </div>
        </div>
    </div>

    <ng-template pTemplate="footer">
        <div class="dialog-footer">
            <p-button label="Annuler" icon="pi pi-times" class="p-button-text" (onClick)="showAnnulationDialog = false">
            </p-button>
            <p-button label="Confirmer l'annulation" icon="pi pi-check" class="p-button-danger"
                (onClick)="confirmAnnulationOrdre()">
            </p-button>
        </div>
    </ng-template>
</p-dialog>

<!-- Dialog d'Envoi de Notification -->
<p-dialog header="Envoyer une Notification" [(visible)]="showNotificationDialog" [modal]="true"
    [style]="{width: '500px'}" [draggable]="false" [resizable]="false" styleClass="modern-dialog">

    <div class="dialog-content">
        <div class="form-group">
            <label for="userId">ID Utilisateur</label>
            <input id="userId" type="text" pInputText [(ngModel)]="notificationRequest.userId"
                placeholder="Entrez l'ID de l'utilisateur" class="w-full">
        </div>

        <div class="form-group">
            <label for="message">Message</label>
            <textarea id="message" pInputTextarea [(ngModel)]="notificationRequest.message"
                placeholder="Entrez votre message..." rows="4" class="w-full">
      </textarea>
        </div>
    </div>

    <ng-template pTemplate="footer">
        <div class="dialog-footer">
            <p-button label="Annuler" icon="pi pi-times" class="p-button-text"
                (onClick)="showNotificationDialog = false">
            </p-button>
            <p-button label="Envoyer" icon="pi pi-send" class="p-button-primary" (onClick)="envoyerNotification()">
            </p-button>
        </div>
    </ng-template>
</p-dialog>

<!-- Toast Messages -->
<p-toast></p-toast>

<!-- Confirmation Dialog -->
<p-confirmDialog></p-confirmDialog>