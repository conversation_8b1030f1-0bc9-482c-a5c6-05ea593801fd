import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { InvestisseurDTO } from '../models/Investisseur';
import { OrdreProposeResponseDTO, OrdreProposeSimulationRequest, OrdreProposeSimulationResponseDTO } from '../models/OrdreP';
import { OrdreResponseDTO } from '../../investisseur/models/ordre';
import { OperationSimulationDTO, OperationResponseDTO, OperationExecutionGroupDTO } from '../models/operation-simulation';
import { KpiDashboardDTO, HistoriqueExecutionDTO, NotificationRequestDTO, NotificationResponseDTO } from '../models/dashboard';

@Injectable({
  providedIn: 'root'
})
export class IntermediaireService {
  private apiUrl = 'http://localhost:8080/api/intermediaire';


  constructor(private http: HttpClient) { }

  getInvestisseurs(): Observable<InvestisseurDTO[]> {
    return this.http.get<InvestisseurDTO[]>(`${this.apiUrl}/clients`);
  }
  getAllOrdresProposes(): Observable<OrdreProposeResponseDTO[]> {
    return this.http.get<OrdreProposeResponseDTO[]>(`${this.apiUrl}/OrdresProposition`);
  }
  getPropositionsPourInvestisseur(username: string): Observable<OrdreProposeResponseDTO[]> {
    return this.http.get<OrdreProposeResponseDTO[]>(`${this.apiUrl}/par-investisseur/${username}`);
  }
  simulerOrdre(request: OrdreProposeSimulationRequest): Observable<OrdreProposeSimulationResponseDTO> {
    return this.http.post<OrdreProposeSimulationResponseDTO>(`${this.apiUrl}/simulation`, request);
  }

  approuverOrdre(request: OrdreProposeSimulationRequest): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/approuver`, request);
  }

  refuserOrdre(ordreProposeId: string): Observable<void> {
    const payload = { ordreProposeId };
    return this.http.post<void>(`${this.apiUrl}/refuser`, payload);
  }


  getOrdresValides(): Observable<OrdreResponseDTO[]> {
    return this.http.get<OrdreResponseDTO[]>(`${this.apiUrl}/ordres/valides`);
  }
  getOrdresEnExecution(): Observable<OrdreResponseDTO[]> {
    return this.http.get<OrdreResponseDTO[]>(`${this.apiUrl}/ordres/enExecution`);
  }
  getOrdresExecute(): Observable<OrdreResponseDTO[]> {
    return this.http.get<OrdreResponseDTO[]>(`${this.apiUrl}/ordres/execute`);
  }
  getOrdresSimilaires(ordreId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/ordres/${ordreId}/similaires`);
  }

  regrouperOrdres(ordres: OrdreResponseDTO[]): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/regrouper-ordres`, ordres);
  }

  // Nouvelle méthode pour simuler le fractionnement
  simulerFractionnement(ordreId: string): Observable<OperationSimulationDTO[]> {
    return this.http.get<OperationSimulationDTO[]>(`${this.apiUrl}/ordres/${ordreId}/simuler-fractionnement`);
  }

  // Nouvelle méthode pour valider et envoyer le fractionnement
  validerEtEnvoyerFractionnement(ordreId: string, indexEnvoye: number): Observable<OperationResponseDTO[]> {
    return this.http.post<OperationResponseDTO[]>(`${this.apiUrl}/ordres/${ordreId}/valider-fractionnement/${indexEnvoye}`, {});
  }

  // Méthode pour récupérer les groupes d'exécution d'opérations
  getOperationExecutionGroups(): Observable<OperationExecutionGroupDTO[]> {
    return this.http.get<OperationExecutionGroupDTO[]>(`${this.apiUrl}/operations/execution-groupes`);
  }

  // ===== NOUVELLES MÉTHODES POUR DASHBOARD-B =====

  // Méthode pour récupérer les KPI du dashboard
  getKpiDashboard(): Observable<KpiDashboardDTO> {
    return this.http.get<KpiDashboardDTO>(`${this.apiUrl}/kpi`);
  }

  // Méthode pour annuler un ordre
  annulerOrdre(ordreId: string): Observable<string> {
    return this.http.post<string>(`${this.apiUrl}/annuler-ordre/${ordreId}`, {});
  }

  // Méthode pour envoyer une notification
  envoyerNotification(request: NotificationRequestDTO): Observable<string> {
    return this.http.post<string>(`${this.apiUrl}/envoyer-notification`, request);
  }

  // Méthode pour récupérer l'historique d'exécution
  getHistoriqueExecution(): Observable<HistoriqueExecutionDTO[]> {
    return this.http.get<HistoriqueExecutionDTO[]>(`${this.apiUrl}/historique`);
  }

}

