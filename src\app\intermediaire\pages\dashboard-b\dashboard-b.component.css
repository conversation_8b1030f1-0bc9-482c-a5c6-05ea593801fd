/* ===== VARIABLES ULTRA-MODERNES ===== */
:root {
    --primary-color: #1e3a8a;
    --secondary-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #8b5cf6;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-radius: 16px;
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.12);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== CONTAINER PRINCIPAL ===== */
.dashboard-container {
    padding: 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* ===== HEADER SECTION ===== */
.dashboard-header {
    margin-bottom: 2.5rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
}

.title-section {
    flex: 1;
}

.dashboard-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.dashboard-title i {
    color: #1e3a8a;
    font-size: 2.25rem;
}

.dashboard-subtitle {
    font-size: 1.125rem;
    color: #64748b;
    margin: 0;
    font-weight: 400;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* ===== BOUTONS HEADER ===== */
.refresh-btn {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%) !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

.refresh-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
}

.notification-btn {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%) !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

.notification-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
}

/* ===== SECTION KPI ===== */
.kpi-section {
    margin-bottom: 3rem;
}

.kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.kpi-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #e2e8f0;
    position: relative;
    overflow: hidden;
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1e3a8a, #3b82f6);
}

.kpi-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.primary-card::before {
    background: linear-gradient(90deg, #1e3a8a, #3b82f6);
}

.warning-card::before {
    background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

.success-card::before {
    background: linear-gradient(90deg, #10b981, #34d399);
}

.info-card::before {
    background: linear-gradient(90deg, #8b5cf6, #a855f7);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.icon-container {
    width: 3rem;
    height: 3rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.primary-icon {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
}

.warning-icon {
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

.success-icon {
    background: linear-gradient(135deg, #10b981, #34d399);
}

.info-icon {
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
}

.trend-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #64748b;
}

.trend-indicator.positive {
    color: #10b981;
}

.kpi-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #64748b;
    margin: 0 0 0.5rem 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.kpi-value-container {
    margin-bottom: 0.5rem;
}

.kpi-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    line-height: 1;
}

.kpi-subtitle {
    font-size: 0.875rem;
    color: #64748b;
    margin-bottom: 1rem;
}

.card-footer {
    margin-top: 1rem;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 3px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.primary-progress {
    background: linear-gradient(90deg, #1e3a8a, #3b82f6);
}

.warning-progress {
    background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

.success-progress {
    background: linear-gradient(90deg, #10b981, #34d399);
}

.info-progress {
    background: linear-gradient(90deg, #8b5cf6, #a855f7);
}

/* ===== CONTENT GRID ===== */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    margin-bottom: 3rem;
}

/* ===== CHART SECTION ===== */
.chart-section {
    height: fit-content;
}

.chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-skeleton {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

/* ===== ORDRES SECTION ===== */
.ordres-section {
    height: fit-content;
}

/* ===== HISTORIQUE SECTION ===== */
.historique-section {
    margin-bottom: 2rem;
}

/* ===== CARTES MODERNES ===== */
.modern-card {
    border-radius: 16px !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
    border: 1px solid #e2e8f0 !important;
    overflow: hidden !important;
}

.modern-card .p-card-header {
    background: white !important;
    border-bottom: 1px solid #e2e8f0 !important;
    padding: 1.5rem !important;
}

.modern-card .p-card-body {
    padding: 0 !important;
}

.modern-card .p-card-content {
    padding: 1.5rem !important;
}

.card-title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-title i {
    color: #1e3a8a;
    font-size: 1.125rem;
}

/* ===== TABLES MODERNES ===== */
.modern-table {
    border-radius: 16px !important;
    overflow: hidden !important;
}

.modern-table .p-datatable-header {
    background: #f8fafc !important;
    border: none !important;
    padding: 1rem 1.5rem !important;
}

.modern-table .p-datatable-thead>tr>th {
    background: #f1f5f9 !important;
    color: #1e293b !important;
    font-weight: 600 !important;
    font-size: 0.875rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.05em !important;
    padding: 1rem !important;
    border: none !important;
    border-bottom: 2px solid #e2e8f0 !important;
}

.modern-table .p-datatable-tbody>tr>td {
    padding: 1rem !important;
    border: none !important;
    border-bottom: 1px solid #f1f5f9 !important;
    font-size: 0.875rem !important;
    color: #1e293b !important;
}

.modern-table .p-datatable-tbody>tr:hover {
    background: #f8fafc !important;
}

.modern-table .p-datatable-tbody>tr:last-child>td {
    border-bottom: none !important;
}

.modern-table .p-paginator {
    background: white !important;
    border: none !important;
    border-top: 1px solid #e2e8f0 !important;
    padding: 1rem 1.5rem !important;
}

.text-center {
    text-align: center;
    color: #64748b;
    font-style: italic;
    padding: 2rem !important;
}

/* ===== DIALOGS MODERNES ===== */
.modern-dialog .p-dialog-header {
    background: white !important;
    border-bottom: 1px solid #e2e8f0 !important;
    padding: 1.5rem !important;
    border-radius: 16px 16px 0 0 !important;
}

.modern-dialog .p-dialog-title {
    font-size: 1.25rem !important;
    font-weight: 700 !important;
    color: #1e293b !important;
}

.modern-dialog .p-dialog-content {
    padding: 1.5rem !important;
    background: white !important;
}

.modern-dialog .p-dialog-footer {
    background: #f8fafc !important;
    border-top: 1px solid #e2e8f0 !important;
    padding: 1rem 1.5rem !important;
    border-radius: 0 0 16px 16px !important;
}

.dialog-content {
    padding: 0;
}

.confirmation-message {
    text-align: center;
    padding: 1rem 0;
}

.confirmation-message .warning-icon {
    font-size: 3rem;
    color: #f59e0b;
    margin-bottom: 1rem;
}

.confirmation-message h4 {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 0.5rem 0;
}

.confirmation-message p {
    color: #64748b;
    margin: 0 0 1.5rem 0;
}

.ordre-details {
    background: #f8fafc;
    border-radius: 12px;
    padding: 1rem;
    margin-top: 1rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row .label {
    font-weight: 600;
    color: #64748b;
    font-size: 0.875rem;
}

.detail-row .value {
    font-weight: 600;
    color: #1e293b;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-group input,
.form-group textarea {
    border-radius: 8px !important;
    border: 1px solid #d1d5db !important;
    padding: 0.75rem !important;
    font-size: 0.875rem !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: #1e3a8a !important;
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1) !important;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
    .content-grid {
        grid-template-columns: 1fr;
    }

    .kpi-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .dashboard-title {
        font-size: 2rem;
    }

    .kpi-grid {
        grid-template-columns: 1fr;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .dashboard-title {
        font-size: 1.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .kpi-value {
        font-size: 2rem;
    }

    .modern-table .p-datatable-thead>tr>th,
    .modern-table .p-datatable-tbody>tr>td {
        padding: 0.75rem 0.5rem !important;
        font-size: 0.8rem !important;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.kpi-card,
.modern-card {
    animation: fadeInUp 0.6s ease-out;
}

.kpi-card:nth-child(1) {
    animation-delay: 0.1s;
}

.kpi-card:nth-child(2) {
    animation-delay: 0.2s;
}

.kpi-card:nth-child(3) {
    animation-delay: 0.3s;
}

.kpi-card:nth-child(4) {
    animation-delay: 0.4s;
}

/* ===== UTILITAIRES ===== */
.w-full {
    width: 100% !important;
}